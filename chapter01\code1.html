<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
  </head>
  <body>
    <h2>前端组成的部分</h2>
    <ul>
      <li style="color: hotpink">HTML-结构层</li>
      <li style="color: green">CSS-样式层</li>
      <li style="color: orange">JavaScript-行为交互层</li>
    </ul>
    <h5>HTML</h5>
    <p>
      是一种超文本标记语言，标准通用标记语言下的一个应用，
    <u><i>HTML不是一种编程语言，</i></u> 而是一种标记语言，是网页制作所必备的
    </p>
    <h5>CSS</h5>
    <p>
      <b>CSS（层叠样式表）</b>用于设置HTML页面中的文本内容、图片、音频、视频等元素的样式<sup>[1]</sup>
    </p>
    <h5>JavaScript</h5>
    <p>是一种弱类型脚本语言，也是前端三件套之一</p>
  </body>
</html>
